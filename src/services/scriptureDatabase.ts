import { 
  AnyScripture, 
  ScriptureQuery, 
  ScriptureSearchResult, 
  ReligiousFaith,
  ChristianScripture,
  IslamicScripture,
  JewishScripture,
  HinduScripture,
  BuddhistScripture,
  SikhScripture
} from '../types/religiousTexts';

export class ScriptureDatabase {
  private scriptures: Map<string, AnyScripture> = new Map();
  private indexes: {
    byFaith: Map<ReligiousFaith, Set<string>>;
    byKeyword: Map<string, Set<string>>;
    byReference: Map<string, string>;
    byTags: Map<string, Set<string>>;
  };

  constructor() {
    this.indexes = {
      byFaith: new Map(),
      byKeyword: new Map(),
      byReference: new Map(),
      byTags: new Map()
    };
    this.initializeDatabase();
  }

  private initializeDatabase() {
    // Initialize with existing scriptures and expand
    this.addInitialScriptures();
  }

  private addInitialScriptures() {
    // Convert existing scriptures to new format
    const existingScriptures: ChristianScripture[] = [
      {
        id: 'matthew_7_7',
        faith: 'Christianity',
        book: 'Matthew',
        chapter: 7,
        verse: 7,
        version: 'NIV',
        testament: 'New',
        text: 'Ask and it will be given to you; seek and you will find; knock and the door will be opened to you.',
        translation: 'New International Version',
        tags: ['prayer', 'seeking', 'faith', 'promise']
      },
      {
        id: 'matthew_10_7',
        faith: 'Christianity',
        book: 'Matthew',
        chapter: 10,
        verse: 7,
        version: 'NIV',
        testament: 'New',
        text: 'As you go, proclaim this message: "The kingdom of heaven has come near."',
        translation: 'New International Version',
        tags: ['kingdom', 'proclamation', 'mission', 'gospel']
      },
      {
        id: 'psalm_23_1',
        faith: 'Christianity',
        book: 'Psalm',
        chapter: 23,
        verse: 1,
        version: 'NIV',
        testament: 'Old',
        text: 'The Lord is my shepherd, I lack nothing.',
        translation: 'New International Version',
        tags: ['shepherd', 'provision', 'trust', 'comfort']
      },
      {
        id: 'john_3_16',
        faith: 'Christianity',
        book: 'John',
        chapter: 3,
        verse: 16,
        version: 'NIV',
        testament: 'New',
        text: 'For God so loved the world that he gave his one and only Son, that whoever believes in him shall not perish but have eternal life.',
        translation: 'New International Version',
        tags: ['love', 'salvation', 'eternal life', 'sacrifice']
      }
    ];

    // Add sample Islamic scriptures
    const islamicScriptures: IslamicScripture[] = [
      {
        id: 'quran_2_255',
        faith: 'Islam',
        type: 'Quran',
        surah: 2,
        ayah: 255,
        surahName: 'Al-Baqarah',
        surahNameArabic: 'البقرة',
        text: 'Allah - there is no deity except Him, the Ever-Living, the Sustainer of existence.',
        originalText: 'اللَّهُ لَا إِلَٰهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ',
        translation: 'Sahih International',
        tags: ['tawhid', 'unity', 'allah', 'ayat al-kursi']
      },
      {
        id: 'quran_1_1',
        faith: 'Islam',
        type: 'Quran',
        surah: 1,
        ayah: 1,
        surahName: 'Al-Fatihah',
        surahNameArabic: 'الفاتحة',
        text: 'In the name of Allah, the Entirely Merciful, the Especially Merciful.',
        originalText: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
        translation: 'Sahih International',
        tags: ['bismillah', 'mercy', 'opening', 'al-fatihah']
      }
    ];

    // Add sample Hindu scriptures
    const hinduScriptures: HinduScripture[] = [
      {
        id: 'bhagavad_gita_2_47',
        faith: 'Hinduism',
        type: 'Epics',
        epic: 'Mahabharata',
        chapter: 2,
        verse: 47,
        text: 'You have a right to perform your prescribed duty, but not to the fruits of action. Never consider yourself the cause of the results of your activities, and never be attached to not doing your duty.',
        originalText: 'कर्मण्येवाधिकारस्ते मा फलेषु कदाचन। मा कर्मफलहेतुर्भूर्मा ते सङ्गोऽस्त्वकर्मणि॥',
        transliteration: 'karmaṇy evādhikāras te mā phaleṣu kadācana mā karma-phala-hetur bhūr mā te saṅgo \'stv akarmaṇi',
        tags: ['karma', 'duty', 'detachment', 'action']
      }
    ];

    // Add sample Buddhist scriptures
    const buddhistScriptures: BuddhistScripture[] = [
      {
        id: 'dhammapada_1_1',
        faith: 'Buddhism',
        type: 'Tripitaka',
        tradition: 'Theravada',
        text: 'All mental phenomena have mind as their forerunner; they have mind as their chief; they are mind-made.',
        text: 'Dhammapada',
        chapter: 1,
        verse: 1,
        tags: ['mind', 'mental phenomena', 'consciousness', 'dhamma']
      }
    ];

    // Add all scriptures to database
    [...existingScriptures, ...islamicScriptures, ...hinduScriptures, ...buddhistScriptures]
      .forEach(scripture => this.addScripture(scripture));
  }

  addScripture(scripture: AnyScripture): void {
    this.scriptures.set(scripture.id, scripture);
    this.updateIndexes(scripture);
  }

  private updateIndexes(scripture: AnyScripture): void {
    // Index by faith
    if (!this.indexes.byFaith.has(scripture.faith)) {
      this.indexes.byFaith.set(scripture.faith, new Set());
    }
    this.indexes.byFaith.get(scripture.faith)!.add(scripture.id);

    // Index by keywords (from text)
    const keywords = this.extractKeywords(scripture.text);
    keywords.forEach(keyword => {
      if (!this.indexes.byKeyword.has(keyword)) {
        this.indexes.byKeyword.set(keyword, new Set());
      }
      this.indexes.byKeyword.get(keyword)!.add(scripture.id);
    });

    // Index by reference
    const reference = this.generateReference(scripture);
    if (reference) {
      this.indexes.byReference.set(reference.toLowerCase(), scripture.id);
    }

    // Index by tags
    if (scripture.tags) {
      scripture.tags.forEach(tag => {
        if (!this.indexes.byTags.has(tag)) {
          this.indexes.byTags.set(tag, new Set());
        }
        this.indexes.byTags.get(tag)!.add(scripture.id);
      });
    }
  }

  private extractKeywords(text: string): string[] {
    return text
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2)
      .filter(word => !this.isStopWord(word));
  }

  private isStopWord(word: string): boolean {
    const stopWords = new Set([
      'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
      'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had',
      'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might',
      'a', 'an', 'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it',
      'we', 'they', 'me', 'him', 'her', 'us', 'them', 'my', 'your', 'his', 'her',
      'its', 'our', 'their'
    ]);
    return stopWords.has(word);
  }

  private generateReference(scripture: AnyScripture): string | null {
    switch (scripture.faith) {
      case 'Christianity':
        const christian = scripture as ChristianScripture;
        return `${christian.book} ${christian.chapter}:${christian.verse}`;
      
      case 'Islam':
        const islamic = scripture as IslamicScripture;
        if (islamic.type === 'Quran') {
          return `Quran ${islamic.surah}:${islamic.ayah}`;
        }
        return null;
      
      case 'Judaism':
        const jewish = scripture as JewishScripture;
        if (jewish.book && jewish.chapter && jewish.verse) {
          return `${jewish.book} ${jewish.chapter}:${jewish.verse}`;
        }
        return null;
      
      case 'Hinduism':
        const hindu = scripture as HinduScripture;
        if (hindu.type === 'Epics' && hindu.epic === 'Mahabharata' && hindu.chapter && hindu.verse) {
          return `Bhagavad Gita ${hindu.chapter}:${hindu.verse}`;
        }
        return null;
      
      case 'Buddhism':
        const buddhist = scripture as BuddhistScripture;
        if (buddhist.text && buddhist.chapter && buddhist.verse) {
          return `${buddhist.text} ${buddhist.chapter}:${buddhist.verse}`;
        }
        return null;
      
      default:
        return null;
    }
  }

  search(query: ScriptureQuery): ScriptureSearchResult {
    let resultIds = new Set<string>();
    let isFirstFilter = true;

    // Filter by faith
    if (query.faith) {
      const faiths = Array.isArray(query.faith) ? query.faith : [query.faith];
      const faithIds = new Set<string>();
      faiths.forEach(faith => {
        const ids = this.indexes.byFaith.get(faith);
        if (ids) {
          ids.forEach(id => faithIds.add(id));
        }
      });
      resultIds = faithIds;
      isFirstFilter = false;
    }

    // Filter by keyword
    if (query.keyword) {
      const keywordIds = this.indexes.byKeyword.get(query.keyword.toLowerCase()) || new Set();
      if (isFirstFilter) {
        resultIds = keywordIds;
        isFirstFilter = false;
      } else {
        resultIds = new Set([...resultIds].filter(id => keywordIds.has(id)));
      }
    }

    // Filter by reference
    if (query.reference) {
      const referenceId = this.indexes.byReference.get(query.reference.toLowerCase());
      if (referenceId) {
        if (isFirstFilter) {
          resultIds = new Set([referenceId]);
          isFirstFilter = false;
        } else {
          resultIds = new Set([...resultIds].filter(id => id === referenceId));
        }
      } else {
        resultIds = new Set(); // No match found
      }
    }

    // Filter by tags
    if (query.tags && query.tags.length > 0) {
      const tagIds = new Set<string>();
      query.tags.forEach(tag => {
        const ids = this.indexes.byTags.get(tag);
        if (ids) {
          ids.forEach(id => tagIds.add(id));
        }
      });
      if (isFirstFilter) {
        resultIds = tagIds;
        isFirstFilter = false;
      } else {
        resultIds = new Set([...resultIds].filter(id => tagIds.has(id)));
      }
    }

    // If no filters applied, return all scriptures
    if (isFirstFilter) {
      resultIds = new Set(this.scriptures.keys());
    }

    // Convert IDs to scriptures
    const allResults = Array.from(resultIds)
      .map(id => this.scriptures.get(id))
      .filter((scripture): scripture is AnyScripture => scripture !== undefined);

    // Apply pagination
    const offset = query.offset || 0;
    const limit = query.limit || 10;
    const scriptures = allResults.slice(offset, offset + limit);

    return {
      scriptures,
      total: allResults.length,
      query,
      suggestions: this.generateSuggestions(query, allResults.length === 0)
    };
  }

  private generateSuggestions(query: ScriptureQuery, noResults: boolean): string[] {
    if (!noResults) return [];

    const suggestions: string[] = [];
    
    if (query.reference) {
      suggestions.push('Try a different reference format (e.g., "Matthew 5:16", "Quran 2:255")');
    }
    
    if (query.keyword) {
      suggestions.push('Try different keywords or check spelling');
      suggestions.push('Use broader terms (e.g., "love" instead of "loving")');
    }

    return suggestions;
  }

  getScriptureById(id: string): AnyScripture | undefined {
    return this.scriptures.get(id);
  }

  getScripturesByFaith(faith: ReligiousFaith): AnyScripture[] {
    const ids = this.indexes.byFaith.get(faith) || new Set();
    return Array.from(ids)
      .map(id => this.scriptures.get(id))
      .filter((scripture): scripture is AnyScripture => scripture !== undefined);
  }

  getAllFaiths(): ReligiousFaith[] {
    return Array.from(this.indexes.byFaith.keys());
  }

  getStats() {
    const stats: Record<ReligiousFaith, number> = {} as Record<ReligiousFaith, number>;
    this.indexes.byFaith.forEach((ids, faith) => {
      stats[faith] = ids.size;
    });
    return {
      total: this.scriptures.size,
      byFaith: stats,
      totalKeywords: this.indexes.byKeyword.size,
      totalTags: this.indexes.byTags.size
    };
  }
}

// Singleton instance
export const scriptureDB = new ScriptureDatabase();
